BaseURL = http://localhost:8000/api/v1

### Key Responses:
-  TemplateListResponse
```json
{
  "templates": [
    {
      "created_at": "2019-08-24T14:15:22Z",
      "updated_at": "2019-08-24T14:15:22Z",
      "id": "string",
      "name": "string",
      "description": "string",
      "language": "string",
      "features": [],
      "supported_sections": [
        "personal_info",
        "education",
        "work_experience",
        "skills",
        "references"
      ],
      "preview_image": "string",
      "configuration": {
        "page_size": "A4",
        "margins": "40pt",
        "font_family": "Helvetica",
        "color_scheme": {
          "primary": "#005A9C",
          "text": "#333333",
          "secondary": "#444444",
          "border": "#CCCCCC"
        },
        "supports_photo": true,
        "supports_certificates": true,
        "supports_cover_letter": true,
        "max_pages": 0,
        "sections": {
          "personal_info": {
            "layout": "two_column",
            "required": true
          },
          "education": {
            "layout": "chronological",
            "required": false,
            "sort_order": "desc"
          },
          "work_experience": {
            "layout": "chronological",
            "required": false,
            "sort_order": "desc"
          },
          "skills": {
            "layout": "categorized",
            "required": false
          },
          "references": {
            "layout": "list",
            "required": false
          },
          "cover_letter": {
            "layout": "letter",
            "required": false
          }
        }
      }
    }
  ],
  "total": 0
}
```
```json
{
  "created_at": "2019-08-24T14:15:22Z",
  "updated_at": "2019-08-24T14:15:22Z",
  "id": "string",
  "name": "string",
  "description": "string",
  "language": "string",
  "features": [],
  "supported_sections": [
    "personal_info",
    "education",
    "work_experience",
    "skills",
    "references"
  ],
  "preview_image": "string",
  "configuration": {
    "page_size": "A4",
    "margins": "40pt",
    "font_family": "Helvetica",
    "color_scheme": {
      "primary": "#005A9C",
      "text": "#333333",
      "secondary": "#444444",
      "border": "#CCCCCC"
    },
    "supports_photo": true,
    "supports_certificates": true,
    "supports_cover_letter": true,
    "max_pages": 0,
    "sections": {
      "personal_info": {
        "layout": "two_column",
        "required": true
      },
      "education": {
        "layout": "chronological",
        "required": false,
        "sort_order": "desc"
      },
      "work_experience": {
        "layout": "chronological",
        "required": false,
        "sort_order": "desc"
      },
      "skills": {
        "layout": "categorized",
        "required": false
      },
      "references": {
        "layout": "list",
        "required": false
      },
      "cover_letter": {
        "layout": "letter",
        "required": false
      }
    }
  }
}
```


- Get Templates(GET)
route: /templates
Get list of available CV templates.
Returns all available templates with their metadata, features, and configuration details.
Args: current_user: Current authenticated user
Returns: TemplateListResponse: List of available templates
Raises: HTTPException: If user not authenticated

- Get Template Details(GET)
- route: /templates/{template_id}
Get detailed information about a specific template.
Returns comprehensive template information including configuration, supported sections, and features.
Args: template_id: Template unique identifier current_user: Current authenticated user
Returns: TemplateResponse: Template details
Raises: HTTPException: If template not found or user not authenticated


- Generate Template Preview(GET)
route: /templates/{template_id}/preview
Generate a preview image of the template with sample data.
Creates a visual representation of the template layout and styling that can be used for template selection in the frontend.
Args: template_id: Template unique identifier format: Image format (png, jpg, jpeg) width: Preview width in pixels (100-1200) height: Preview height in pixels (150-1800) primary_color: Primary color override (hex format) current_user: Current authenticated user
Returns: Response: Binary image data with appropriate content type
Raises: HTTPException: If template not found or preview generation fails
