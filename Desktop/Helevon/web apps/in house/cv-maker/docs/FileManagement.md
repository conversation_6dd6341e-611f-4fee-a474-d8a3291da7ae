BaseURL = http://localhost:8000/api/v1

### Key Responses: 
-  SuccessResponse
```json
{
  "message": "string",
  "data": {}
}
```

- FileResponse
```json
{
  "created_at": "2019-08-24T14:15:22Z",
  "updated_at": "2019-08-24T14:15:22Z",
  "name": "string",
  "type": "string",
  "size": 0,
  "category": "string",
  "id": "string",
  "user_id": "string",
  "cv_id": "string",
  "url": "string"
}
```

- FileDeleteResponse
```json
{
  "message": "string",
  "file_id": "string"
}
```

- FileListResponse
```json
[
  {
    "id": "string",
    "name": "string",
    "type": "string",
    "size": 0,
    "category": "string",
    "url": "string",
    "created_at": "string"
  }
]
```


- Upload Profile Photo(POST)
route: /cv/{cv_id}/photo
Upload profile photo for a CV.
Args: cv_id: CV unique identifier file: Photo file to upload (JPG, PNG only) current_user: Current authenticated user db: Database session
Returns: FileResponse: Uploaded photo information
Raises: HTTPException: If upload fails or validation errors occur
Request Body schema: multipart/form-data
file(required)	
string <binary> (File) 

- Upload Certificate(POST)
route: /cv/{cv_id}/education/{education_id}/certificate
Upload certificate for an education entry.
Args: cv_id: CV unique identifier education_id: Education entry identifier file: Certificate file to upload (PDF, JPG, PNG only) current_user: Current authenticated user db: Database session
Returns: FileResponse: Uploaded certificate information
Raises: HTTPException: If upload fails or validation errors occur


- Get File(GET) [can be used to get profile photo and certificates with the file id for viewing]
route: /cv/files/{file_id}
Retrieve a file by ID (returns the actual file content).
Args: file_id: File unique identifier current_user: Current authenticated user db: Database session
Returns: Response: File content with appropriate headers
Raises: HTTPException: If file not found or access denied


- Delete File(DELETE)
route: /cv/files/{file_id}
Delete a file by ID.
Args: file_id: File unique identifier current_user: Current authenticated user db: Database session
Returns: FileDeleteResponse: Deletion confirmation
Raises: HTTPException: If file not found or access denied

- Upload File(POST)[Files not linked to an education but needed in cv]
route: /cv/{cv_id}/upload
Upload a file associated with a CV.
Args: cv_id: CV unique identifier file: File to upload category: File category (photo, certificate, cover_letter, other) current_user: Current authenticated user db: Database session
Returns: FileResponse: Uploaded file information
Raises: HTTPException: If upload fails or validation errors occur

- Get File(GET)
route: /cv/{cv_id}/file/{file_id}
Retrieve a file by ID.
Args: cv_id: CV unique identifier file_id: File unique identifier current_user: Current authenticated user db: Database session
Returns: Response: Binary file data with appropriate Content-Type header
Raises: HTTPException: If file not found or access denied

- Delete File(DELETE)
route: /cv/{cv_id}/file/{file_id}
Delete a file.
Args: cv_id: CV unique identifier file_id: File unique identifier current_user: Current authenticated user db: Database session
Returns: FileDeleteResponse: Deletion confirmation
Raises: HTTPException: If file not found or access denied


- Get Cv Files(GET)
route: /cv/{cv_id}/file/{file_id}
Get all files associated with a CV.
Args: cv_id: CV unique identifier current_user: Current authenticated user db: Database session
Returns: List[FileListResponse]: List of files


- Get Standalone File
route: /file/{file_id}
Retrieve a standalone file by ID (not associated with CV).
Args: file_id: File unique identifier current_user: Current authenticated user db: Database session
Returns: Response: Binary file data with appropriate Content-Type header
