BaseURL = http://localhost:8000/api/v1
SuccessResponse (to be used where response model is not given):
```json
{
  "message": "string",
  "data": {}
}
```

# Authentication and User Management:

## Authentication 
- Register a new user account.(POST)
route: /auth/register
Args: user_data: User registration data db: Database session
Returns: UserResponse: Created user data
Raises: HTTPException: If email already exists or registration fails
payload:
```json
{
  "name": "string",
  "email": "<EMAIL>",
  "language": "string",
  "password": "stringst",
  "confirm_password": "string"
}
```
language accepted data: en|de|ar
language is to be used for internalization, multi language for the app, ar is for arabic

response sample:
```json
{
  "created_at": "2019-08-24T14:15:22Z",
  "updated_at": "2019-08-24T14:15:22Z",
  "name": "string",
  "email": "<EMAIL>",
  "language": "string",
  "id": "string",
  "email_verified": "2019-08-24T14:15:22Z",
  "last_login_at": "2019-08-24T14:15:22Z"
}
```

- Login User (POST)
route: /auth/signin
Authenticate user and return access/refresh tokens.
Args: login_data: User login credentials db: Database session
Returns: TokenResponse: JWT tokens and metadata
Raises: HTTPException: If credentials are invalid or account is locked
payload:
```json
{
  "email": "<EMAIL>",
  "password": "string"
}
```
response sample:
```json
{
  "access_token": "string",
  "refresh_token": "string",
  "token_type": "bearer",
  "expires_in": 0
}
```

- Refresh access token using refresh token.(POST)
route: /auth/refresh-token
Args: refresh_data: Refresh token request db: Database session
Returns: TokenResponse: New access token
Raises: HTTPException: If refresh token is invalid
payload:
```json
{
  "refresh_token": "string"
}
```
response sample:
```json
{
  "access_token": "string",
  "refresh_token": "string",
  "token_type": "bearer",
  "expires_in": 0
}
```

- Logout user by invalidating refresh token.(POST)
route: /auth/signout
Args: current_user: Current authenticated user db: Database session
Returns: SuccessResponse: Logout confirmation

## User management
### required
Authorizations:
HTTPBearer
HTTP: HTTPBearer
HTTP Authorization Scheme: bearer

- Get User Account(GET)
route: /user/account
Get current user account information.
Args: current_user: Current authenticated user
Returns: UserResponse: User account information
response sample:
```json
{
  "created_at": "2019-08-24T14:15:22Z",
  "updated_at": "2019-08-24T14:15:22Z",
  "name": "string",
  "email": "<EMAIL>",
  "language": "string",
  "id": "string",
  "email_verified": "2019-08-24T14:15:22Z",
  "last_login_at": "2019-08-24T14:15:22Z"
}
```

- Update User Account(PUT)
route: /user/account
Update user account information.
Args: user_update: User update data current_user: Current authenticated user db: Database session
Returns: UserResponse: Updated user information
Raises: HTTPException: If update fails or validation errors occur
payload:
```json
{
  "name": "string",
  "email": "<EMAIL>",
  "language": "string",
  "current_password": "string",
  "new_password": "stringst",
  "confirm_password": "string"
}
```
response sample:
```json
{
  "created_at": "2019-08-24T14:15:22Z",
  "updated_at": "2019-08-24T14:15:22Z",
  "name": "string",
  "email": "<EMAIL>",
  "language": "string",
  "id": "string",
  "email_verified": "2019-08-24T14:15:22Z",
  "last_login_at": "2019-08-24T14:15:22Z"
}
```

- Get User Profile(GET)
route: /user/profile
Get detailed user profile information.
Args: current_user: Current authenticated user
Returns: UserProfile: Detailed user profile
response sample:
```json
{
  "created_at": "2019-08-24T14:15:22Z",
  "updated_at": "2019-08-24T14:15:22Z",
  "name": "string",
  "email": "<EMAIL>",
  "language": "string",
  "id": "string",
  "email_verified": "2019-08-24T14:15:22Z",
  "last_login_at": "2019-08-24T14:15:22Z",
  "login_attempts": 0,
  "locked_until": "2019-08-24T14:15:22Z"
}
```